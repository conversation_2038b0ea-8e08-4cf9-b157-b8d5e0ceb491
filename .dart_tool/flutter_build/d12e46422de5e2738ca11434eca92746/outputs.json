["/Users/<USER>/Desktop/66/build/web/*/index.html", "/Users/<USER>/Desktop/66/build/web/flutter_bootstrap.js", "/Users/<USER>/Desktop/66/build/web/main.dart.js", "build/web/assets/assets/images/logo.webp", "build/web/assets/hosted_vimeo_player.html", "build/web/assets/packages/awesome_notifications/test/assets/images/test_image.png", "build/web/assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "build/web/assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html", "build/web/assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css", "build/web/assets/packages/flutter_inappwebview_web/assets/web/web_support.js", "build/web/assets/packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf", "build/web/assets/packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf", "build/web/assets/packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf", "build/web/assets/packages/wakelock_plus/assets/no_sleep.js", "build/web/assets/fonts/MaterialIcons-Regular.otf", "build/web/assets/shaders/ink_sparkle.frag", "build/web/assets/AssetManifest.json", "build/web/assets/AssetManifest.bin", "build/web/assets/AssetManifest.bin.json", "build/web/assets/FontManifest.json", "build/web/assets/NOTICES", "build/web/splash/img/light-2x.png", "build/web/splash/img/dark-4x.png", "build/web/splash/img/light-3x.png", "build/web/splash/img/dark-3x.png", "build/web/splash/img/light-4x.png", "build/web/splash/img/dark-2x.png", "build/web/splash/img/dark-1x.png", "build/web/splash/img/light-1x.png", "build/web/favicon.png", "build/web/icons/Icon-192.png", "build/web/icons/Icon-maskable-192.png", "build/web/icons/Icon-maskable-512.png", "build/web/icons/Icon-512.png", "build/web/manifest.json", "build/web/sw.js", "build/web/flutter_service_worker.js"]