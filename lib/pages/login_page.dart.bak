import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import '../services/api_service.dart';
import '../services/user_service.dart';
import '../services/auth_service.dart';
import '../services/persistent_auth_service.dart';
import '../config/app_config.dart';
import '../design_system/kft_design_system.dart';
import '../widgets/kft_button.dart';
import '../widgets/kft_text_field.dart';
import '../widgets/pin_input_widget.dart';
import '../widgets/premium_animated_logo.dart';
import '../widgets/network_error_dialog.dart';
import '../utils/animations.dart';
import 'dart:math';
import 'package:flutter/foundation.dart' show kIsWeb;
// For web device id
// ignore: avoid_web_libraries_in_flutter
// import 'dart:html' as html;
// Only import on non-web platforms
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io' show Platform;
import '../utils/platform_storage.dart';
import '../theme/app_theme.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart' as provider;
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import '../providers/auth_provider.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class LoginPage extends riverpod.ConsumerStatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  riverpod.ConsumerState<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends riverpod.ConsumerState<LoginPage> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _pinController = TextEditingController();
  bool _showRegistrationForm = false;
  final _nameController = TextEditingController();

  bool _isLoading = false;
  String _errorMessage = '';
  bool _showWhatsAppSupport = false;
  String _whatsappNumber = '**********';

  // Removed background animation for minimalistic design

  final ApiService _apiService = ApiService();
  final UserService _userService = UserService();
  final AuthService _authService = AuthService();

  // Import the new persistent auth service
  late final PersistentAuthService _persistentAuthService;

  bool _showThankYou = false;

  // New: Two-step login state
  bool _phoneStepComplete = false;

  bool _isKeyboardVisible = false;
  final FocusNode _phoneFocusNode = FocusNode();

  // Add a new state variable to track if the user is typing in the phone field
  bool _isTypingPhone = false;

  // Remember me checkbox state
  bool _rememberMe = true; // Default to true for persistent login

  @override
  void initState() {
    super.initState();
    _showRegistrationForm = false; // Show login form by default

    // Initialize persistent auth service
    _persistentAuthService = PersistentAuthService();

    // Listen for focus changes to detect keyboard
    _phoneFocusNode.addListener(() {
      setState(() {
        _isKeyboardVisible = _phoneFocusNode.hasFocus;
      });
    });
    // Listen for typing in the phone field
    _phoneController.addListener(() {
      setState(() {
        _isTypingPhone = _phoneController.text.isNotEmpty;
      });
    });
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _pinController.dispose();
    _phoneFocusNode.dispose();
    super.dispose();
  }

  Future<String> getDeviceId() async {
    if (kIsWeb) {
      return 'web_device';
    }
    final deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      return androidInfo.id;
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      return iosInfo.identifierForVendor ?? 'unknown_ios_device';
    } else {
      return 'unsupported_platform';
    }
  }

  Future<dynamic> checkPhoneRegistered(String phoneNumber, String deviceId) async {
    // Use local API service instead of hardcoded URL
    final baseUrl = ApiService.baseUrl.replaceAll('/admin/api/', '');
    final url = Uri.parse('${baseUrl}/admin/phone_check.php');
    final response = await http.post(url, body: {
      'phone_number': phoneNumber,
      'device_id': deviceId,
    });
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data['error'] == 'DEVICE_ALREADY_REGISTERED') {
        return 'DEVICE_ALREADY_REGISTERED';
      }
      return data['exists'] == true;
    }
    throw Exception('Failed to check phone number');
  }

  Future<void> _checkPhone() async {
    final phone = _phoneController.text.trim();
    if (phone.isEmpty || phone.length < 10) {
      setState(() {
        _errorMessage = 'Enter a valid phone number';
      });
      return;
    }
    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _showWhatsAppSupport = false;
    });
    try {
      final deviceId = await getDeviceId();
      final exists = await checkPhoneRegistered(phone, deviceId);
      if (exists == 'DEVICE_ALREADY_REGISTERED') {
        setState(() {
          _errorMessage = 'Already logged in on another device. Please contact admin.';
          _showWhatsAppSupport = true;
          _isLoading = false;
        });
        return;
      }
      if (exists) {
        setState(() {
          _phoneStepComplete = true;
          _errorMessage = '';
        });
      } else {
        setState(() {
          _showRegistrationForm = true;
          _errorMessage = 'This number is not registered. Please complete the registration form below.';
          _showWhatsAppSupport = true;
        });
      }
    } catch (e) {
      final errorStr = e.toString().toLowerCase();

      // Check for network-related errors
      if (errorStr.contains('socketexception') ||
          errorStr.contains('connection refused') ||
          errorStr.contains('certificate_verify_failed') ||
          errorStr.contains('handshakeexception') ||
          errorStr.contains('network error') ||
          errorStr.contains('timeout')) {

        // Show network error dialog with retry option
        if (mounted) {
          context.showNetworkError(
            e.toString(),
            onRetry: () => _checkPhone(),
          );
        }
      } else {
        setState(() {
          _errorMessage = 'Failed to check phone number.';
        });
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _login() async {
    final phone = _phoneController.text.trim();
    final pin = _pinController.text.trim();
    print('DEBUG: Attempting login with phone: '
        '[32m$phone[0m and pin: [33m$pin[0m');
    if (pin.isEmpty || pin.length != 4 || !RegExp(r'^\d{4}').hasMatch(pin)) {
      setState(() {
        _errorMessage = 'Please enter a valid 4-digit PIN to continue';
      });
      print('DEBUG: Invalid PIN entered');
      return;
    }
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });
    try {
      final deviceId = await getDeviceId();
      print('DEBUG: Device ID: [36m$deviceId[0m');
      // Use the new persistent auth service for login
      final result = await _persistentAuthService.login(
        phoneNumber: phone,
        pin: pin,
        rememberMe: _rememberMe,
        deviceId: deviceId,
      );
      print('DEBUG: Login API response: [35m$response[0m');

      // Save authentication data using the new auth service
      await _authService.saveAuthData(
        token: response['token'],
        userId: response['user_id'],
        userName: response['user_name'] ?? response['name'],
      );

      final userProfile = await _apiService.getUserProfile();
      print('DEBUG: User profile response: [34m$userProfile[0m');
      if (userProfile == null) {
        setState(() {
          _errorMessage = 'Failed to load user profile. Please try again or contact support.';
          _showWhatsAppSupport = true;
        });
        print('DEBUG: Failed to load user profile');
        return;
      }
      await _userService.saveUserProfile(userProfile);
      if (mounted) {
        await ref.read(authProvider.notifier).login(response['token']);
        print('DEBUG: Navigation to /home');
        Navigator.of(context).pushReplacementNamed('/home');
      }
    } catch (e, stack) {
      print('DEBUG: Login error: [31m$e[0m');
      print('DEBUG: Stack trace: $stack');

      final errorStr = e.toString().toLowerCase();

      // Device restriction logic
      bool isDeviceRestriction = false;
      try {
        final errorJson = jsonDecode(e.toString());
        if (errorJson is Map && (errorJson['error'] == 'already_logged_in' || errorJson['error'] == 'already_logged_in ' || errorJson['error'] == 'device_mismatch')) {
          isDeviceRestriction = true;
        }
      } catch (_) {}

      if (isDeviceRestriction) {
        setState(() {
          _errorMessage = 'Already logged in on another device. Please contact admin.';
          _showWhatsAppSupport = true;
          _isLoading = false;
        });
        return;
      }

      // Check for network-related errors
      if (errorStr.contains('socketexception') ||
          errorStr.contains('connection refused') ||
          errorStr.contains('certificate_verify_failed') ||
          errorStr.contains('handshakeexception') ||
          errorStr.contains('network error') ||
          errorStr.contains('timeout')) {

        setState(() {
          _isLoading = false;
        });

        // Show network error dialog with retry option
        if (mounted) {
          context.showNetworkError(
            e.toString(),
            onRetry: () => _login(),
          );
        }
      } else {
        setState(() {
          _errorMessage = 'Login failed. Please try again.';
          _showWhatsAppSupport = false;
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    _isKeyboardVisible = keyboardHeight > 0;

    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: Stack(
        children: [
          // Enhanced Animated Gradient Background with floating orbs
          Positioned.fill(
            child: Stack(
              children: [
                _buildAnimatedGradientBackground(context),
                // Floating Gradient Orbs for Enhanced Visual Appeal
                Positioned(
                  top: -100,
                  right: -100,
                  child: TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 3000),
                    tween: Tween(begin: 0.0, end: 1.0),
                    builder: (context, value, child) {
                      return AnimatedContainer(
                        duration: const Duration(milliseconds: 2000),
                        width: 300,
                        height: 300,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              Colors.white.withOpacity((isDarkMode ? 0.05 : 0.1) * value),
                              Colors.transparent,
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
                Positioned(
                  bottom: -150,
                  left: -150,
                  child: TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 3500),
                    tween: Tween(begin: 0.0, end: 1.0),
                    builder: (context, value, child) {
                      return AnimatedContainer(
                        duration: const Duration(milliseconds: 2500),
                        width: 400,
                        height: 400,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              Colors.white.withOpacity((isDarkMode ? 0.04 : 0.08) * value),
                              Colors.transparent,
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
                BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 16, sigmaY: 16),
                  child: Container(
                    color: Colors.black.withOpacity(isDarkMode ? 0.3 : 0.08),
                  ),
                ),
              ],
            ),
          ),
          // Main Content
          SafeArea(
            child: LayoutBuilder(
              builder: (context, constraints) {
                final availableHeight = constraints.maxHeight;
                return SingleChildScrollView(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(minHeight: availableHeight),
                    child: IntrinsicHeight(
                      child: Column(
                        children: [
                          // Enhanced Top Section: Logo & App Name in premium glass card
                          if (!_isKeyboardVisible || !_phoneStepComplete)
                            Padding(
                              padding: const EdgeInsets.only(top: 40, bottom: 16),
                              child: Center(
                                child: EnhancedGlassCard(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      // Premium Logo Container with Glow Effect
                                      Container(
                                        width: _isKeyboardVisible ? 64 : 100,
                                        height: _isKeyboardVisible ? 64 : 100,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.circular(_isKeyboardVisible ? 20 : 32),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.white.withOpacity(0.3),
                                              blurRadius: 20,
                                              offset: const Offset(0, 8),
                                            ),
                                            BoxShadow(
                                              color: Colors.black.withOpacity(0.1),
                                              blurRadius: 30,
                                              offset: const Offset(0, 15),
                                            ),
                                          ],
                                        ),
                                        child: ClipRRect(
                                          borderRadius: BorderRadius.circular(_isKeyboardVisible ? 20 : 32),
                                          child: Padding(
                                            padding: EdgeInsets.all(_isKeyboardVisible ? 12 : 20),
                                            child: Image.asset(
                                              'assets/images/logo.png',
                                              fit: BoxFit.contain,
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: _isKeyboardVisible ? 12 : 20),
                                      // Enhanced App Title
                                      Text(
                                        'KFT Fitness',
                                        style: TextStyle(
                                          fontSize: _isKeyboardVisible ? 20 : 28,
                                          fontWeight: FontWeight.w800,
                                          color: Colors.white,
                                          letterSpacing: 1.5,
                                          shadows: [
                                            Shadow(
                                              color: Colors.black.withOpacity(0.3),
                                              offset: const Offset(0, 2),
                                              blurRadius: 4,
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(height: _isKeyboardVisible ? 4 : 8),
                                      // Enhanced Subtitle
                                      Text(
                                        'Transform Your Body & Mind',
                                        style: TextStyle(
                                          fontSize: _isKeyboardVisible ? 12 : 16,
                                          color: Colors.white.withOpacity(0.95),
                                          letterSpacing: 0.8,
                                          fontWeight: FontWeight.w500,
                                          shadows: [
                                            Shadow(
                                              color: Colors.black.withOpacity(0.2),
                                              offset: const Offset(0, 1),
                                              blurRadius: 2,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          // Enhanced Fitness Icons Row with Better Animations
                          AnimatedSwitcher(
                            duration: const Duration(milliseconds: 400),
                            transitionBuilder: (child, animation) {
                              return FadeTransition(
                                opacity: animation,
                                child: SlideTransition(
                                  position: Tween<Offset>(
                                    begin: const Offset(0, 0.3),
                                    end: Offset.zero,
                                  ).animate(CurvedAnimation(
                                    parent: animation,
                                    curve: Curves.easeOutCubic,
                                  )),
                                  child: child,
                                ),
                              );
                            },
                            child: (!_phoneStepComplete && !_showRegistrationForm && !_isTypingPhone)
                                ? Padding(
                                    padding: const EdgeInsets.only(bottom: 20),
                                    child: SizedBox(
                                      height: 70,
                                      child: Center(
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            _buildEnhancedFitnessIcon(Icons.fitness_center, 'Strength'),
                                            const SizedBox(width: 20),
                                            _buildEnhancedFitnessIcon(Icons.directions_run, 'Cardio'),
                                            const SizedBox(width: 20),
                                            _buildEnhancedFitnessIcon(Icons.restaurant_menu, 'Nutrition'),
                                            const SizedBox(width: 20),
                                            _buildEnhancedFitnessIcon(Icons.trending_down, 'Weight Loss'),
                                          ],
                                        ),
                                      ),
                                    ),
                                  )
                                : const SizedBox.shrink(),
                          ),
                          // Enhanced Main Form Container
                          Expanded(
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 600),
                              curve: Curves.easeOutCubic,
                              margin: EdgeInsets.only(
                                top: _isKeyboardVisible ? 12 : 20,
                                left: 20,
                                right: 20,
                                bottom: keyboardHeight > 0 ? 0 : 20,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(36),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.08),
                                    blurRadius: 40,
                                    offset: const Offset(0, 20),
                                  ),
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.04),
                                    blurRadius: 80,
                                    offset: const Offset(0, 40),
                                  ),
                                ],
                              ),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 28),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.stretch,
                                  children: [
                                    // Enhanced Form Header
                                    if (!_isKeyboardVisible || !_phoneStepComplete)
                                      _buildEnhancedFormHeader(theme, _isKeyboardVisible),
                                    // Enhanced Animated form content
                                    AnimatedSwitcher(
                                      duration: const Duration(milliseconds: 400),
                                      transitionBuilder: (child, animation) {
                                        return FadeTransition(
                                          opacity: animation,
                                          child: SlideTransition(
                                            position: Tween<Offset>(
                                              begin: const Offset(0.1, 0),
                                              end: Offset.zero,
                                            ).animate(CurvedAnimation(
                                              parent: animation,
                                              curve: Curves.easeOutCubic,
                                            )),
                                            child: child,
                                          ),
                                        );
                                      },
                                      child: _showThankYou
                                          ? _buildThankYou(theme)
                                          : _showRegistrationForm
                                              ? _buildModernRegistrationForm(theme)
                                              : _phoneStepComplete
                                                  ? _buildModernPinForm(theme)
                                                  : _buildModernPhoneForm(theme),
                                    ),
                                    // Error Message
                                    if (_errorMessage.isNotEmpty)
                                      Padding(
                                        padding: const EdgeInsets.only(top: 16),
                                        child: Column(
                                          children: [
                                            ColoredCard(
                                              color: const Color(0xFFFEF2F2),
                                              borderColor: const Color(0xFFFECACA),
                                              icon: Icons.error_outline,
                                              iconColor: const Color(0xFFDC2626),
                                              text: _errorMessage,
                                              textColor: const Color(0xFFDC2626),
                                            ),
                                            if (_errorMessage == 'Already logged in on another device. Please contact admin.' && _showWhatsAppSupport && !_showRegistrationForm)
                                              Padding(
                                                padding: const EdgeInsets.only(top: 12),
                                                child: SizedBox(
                                                  width: double.infinity,
                                                  child: ElevatedButton.icon(
                                                    onPressed: _launchWhatsAppSupport,
                                                    icon: const FaIcon(FontAwesomeIcons.whatsapp, color: Colors.white),
                                                    label: const Text('Contact Admin on WhatsApp'),
                                                    style: ElevatedButton.styleFrom(
                                                      backgroundColor: Color(0xFF25D366),
                                                      foregroundColor: Colors.white,
                                                      textStyle: const TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
                                                      padding: const EdgeInsets.symmetric(vertical: 14),
                                                      shape: RoundedRectangleBorder(
                                                        borderRadius: BorderRadius.circular(12),
                                                      ),
                                                      elevation: 0,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                    // WhatsApp Support (for other errors)
                                    if (_showWhatsAppSupport && !_showRegistrationForm && !_isKeyboardVisible && _errorMessage != 'Already logged in on another device. Please contact admin.')
                                      Padding(
                                        padding: const EdgeInsets.only(top: 16),
                                        child: TextButton.icon(
                                          onPressed: _launchWhatsAppSupport,
                                          icon: const Icon(Icons.chat, size: 16),
                                          label: const Text('Contact Support'),
                                          style: TextButton.styleFrom(
                                            foregroundColor: const Color(0xFF6366F1),
                                            textStyle: const TextStyle(fontSize: 13),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedGradientBackground(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 2000),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              stops: const [0.0, 0.4, 0.7, 1.0],
              colors: isDarkMode
                  ? [
                      // Dark mode colors
                      Color.lerp(const Color(0xFF121212), const Color(0xFF1E1E1E), value)!,
                      Color.lerp(const Color(0xFF1E1E1E), const Color(0xFF2C2C2C), value)!,
                      Color.lerp(const Color(0xFF2C2C2C), const Color(0xFF3D5AFE).withOpacity(0.3), value)!,
                      Color.lerp(const Color(0xFF3D5AFE).withOpacity(0.2), const Color(0xFF8187FF).withOpacity(0.2), value)!,
                    ]
                  : [
                      // Light mode colors matching homepage
                      Color.lerp(const Color(0xFF3D5AFE), const Color(0xFF6366F1), value)!,
                      Color.lerp(const Color(0xFF5C6BC0), const Color(0xFF7C3AED), value)!,
                      Color.lerp(const Color(0xFF8187FF), const Color(0xFF9333EA), value)!,
                      Color.lerp(const Color(0xFF9C88FF), const Color(0xFFEC4899), value)!,
                    ],
            ),
          ),
          child: Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.topRight,
                radius: 1.5,
                colors: [
                  Colors.white.withOpacity(0.1 * value),
                  Colors.transparent,
                ],
              ),
            ),
            child: Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: Alignment.bottomLeft,
                  radius: 1.2,
                  colors: [
                    Colors.white.withOpacity(0.05 * value),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCleanFitnessIcon(IconData icon, String label, {double iconSize = 40}) {
    return Align(
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: iconSize,
            height: iconSize,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 9,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedFitnessIcon(IconData icon, String label) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 800),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * value),
          child: Opacity(
            opacity: value,
            child: SizedBox(
              width: 60, // Fixed width to prevent overflow
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.white.withOpacity(0.25),
                          Colors.white.withOpacity(0.1),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(14),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Icon(
                      icon,
                      color: Colors.white,
                      size: 22,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    label,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                      shadows: [
                        Shadow(
                          color: Colors.black26,
                          offset: Offset(0, 1),
                          blurRadius: 2,
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFormHeader(ThemeData theme, bool isKeyboardVisible) {
    return Padding(
      padding: EdgeInsets.fromLTRB(
        24,
        isKeyboardVisible ? 8 : 20,
        24,
        isKeyboardVisible ? 4 : 12,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            _phoneStepComplete
              ? 'Enter Your PIN'
              : _showRegistrationForm
                ? 'Create Account'
                : 'Welcome Back',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(0xFF1F2937),
              fontSize: isKeyboardVisible ? 20 : 24,
            ),
          ),
          SizedBox(height: isKeyboardVisible ? 4 : 8),
          Text(
            _phoneStepComplete
              ? 'Please enter your 4-digit PIN'
              : _showRegistrationForm
                ? 'Join our fitness community'
                : 'Sign in to continue your fitness journey',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: const Color(0xFF6B7280),
              fontSize: isKeyboardVisible ? 13 : 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedFormHeader(ThemeData theme, bool isKeyboardVisible) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 600),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Padding(
              padding: EdgeInsets.fromLTRB(
                0,
                isKeyboardVisible ? 8 : 20,
                0,
                isKeyboardVisible ? 8 : 16,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _phoneStepComplete
                        ? 'Enter Your PIN'
                        : _showRegistrationForm
                            ? 'Create Account'
                            : 'Welcome Back',
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.w800,
                      color: const Color(0xFF1F2937),
                      fontSize: isKeyboardVisible ? 22 : 28,
                      letterSpacing: -0.5,
                    ),
                  ),
                  SizedBox(height: isKeyboardVisible ? 6 : 12),
                  Text(
                    _phoneStepComplete
                        ? 'Please enter your 4-digit PIN to continue'
                        : _showRegistrationForm
                            ? 'Join our fitness community and start your transformation'
                            : 'Sign in to continue your fitness journey',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: const Color(0xFF6B7280),
                      fontSize: isKeyboardVisible ? 14 : 16,
                      fontWeight: FontWeight.w500,
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildModernPhoneForm(ThemeData theme) {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Phone Input Field - Maintain professional size
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFF9FAFB),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: const Color(0xFFE5E7EB),
              width: 1,
            ),
          ),
          child: TextField(
            controller: _phoneController,
            focusNode: _phoneFocusNode,
            keyboardType: TextInputType.phone,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'[0-9+]')),
            ],
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Color(0xFF1F2937),
            ),
            decoration: InputDecoration(
              hintText: 'Phone Number',
              hintStyle: const TextStyle(
                color: Color(0xFF9CA3AF),
                fontWeight: FontWeight.w400,
                fontSize: 16,
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.all(12),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF6366F1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.phone,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 18, // Consistent professional padding
              ),
            ),
          ),
        ),

        SizedBox(height: isKeyboardVisible ? 8 : 20),

        // Continue Button - Maintain professional appearance
        Container(
          width: double.infinity,
          height: isKeyboardVisible ? 50 : 56, // Slightly smaller but still professional
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                theme.colorScheme.primary,
                theme.colorScheme.primary.withOpacity(0.8),
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.primary.withOpacity(isKeyboardVisible ? 0.2 : 0.3),
                blurRadius: isKeyboardVisible ? 8 : 12,
                offset: Offset(0, isKeyboardVisible ? 2 : 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _isLoading ? null : _checkPhone,
              borderRadius: BorderRadius.circular(16),
              child: Center(
                child: _isLoading
                    ? const SizedBox(
                        width: 22,
                        height: 22,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'Continue',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16, // Maintain professional font size
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          SizedBox(width: 8),
                          Icon(
                            Icons.arrow_forward,
                            color: Colors.white,
                            size: 20,
                          ),
                        ],
                      ),
              ),
            ),
          ),
        ),

        // Only show divider and register button when keyboard is not visible
        if (!isKeyboardVisible) ...[
          const SizedBox(height: 20),

          // Divider
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 1,
                  color: const Color(0xFFE5E7EB),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Text(
                  'or',
                  style: TextStyle(
                    color: const Color(0xFF9CA3AF),
                    fontWeight: FontWeight.w500,
                    fontSize: 13,
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  height: 1,
                  color: const Color(0xFFE5E7EB),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Register Button
          Container(
            width: double.infinity,
            height: 52,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(14),
              border: Border.all(
                color: theme.colorScheme.primary,
                width: 1.5,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: _isLoading ? null : () => setState(() => _showRegistrationForm = true),
                borderRadius: BorderRadius.circular(14),
                child: Center(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.person_add_outlined,
                        color: theme.colorScheme.primary,
                        size: 18,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'New User? Register',
                        style: TextStyle(
                          color: theme.colorScheme.primary,
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPhoneForm(ThemeData theme) {
    return _buildModernPhoneForm(theme);
  }

  Widget _buildModernPinForm(ThemeData theme) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Security Icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                theme.colorScheme.primary,
                theme.colorScheme.primary.withOpacity(0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.primary.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: const Icon(
            Icons.security,
            color: Colors.white,
            size: 40,
          ),
        ),

        const SizedBox(height: 32),

        // Modern PIN Input Widget
        PinInputWidget(
          onPinCompleted: (pin) {
            _pinController.text = pin;
            if (!_isLoading) {
              _login();
            }
          },
          onPinChanged: (pin) {
            _pinController.text = pin;
            // Clear error when user starts typing
            if (_errorMessage.isNotEmpty) {
              setState(() {
                _errorMessage = '';
              });
            }
          },
          isLoading: _isLoading,
          errorMessage: _errorMessage.isNotEmpty ? _errorMessage : null,
          pinLength: 4,
          autoFocus: true,
        ),

        const SizedBox(height: 32),

        // Action Buttons
        Row(
          children: [
            Expanded(
              child: Container(
                height: 48,
                decoration: BoxDecoration(
                  color: const Color(0xFFF3F4F6),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _isLoading ? null : () => setState(() => _phoneStepComplete = false),
                    borderRadius: BorderRadius.circular(12),
                    child: Center(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.arrow_back,
                            color: const Color(0xFF6B7280),
                            size: 18,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Back',
                            style: TextStyle(
                              color: const Color(0xFF6B7280),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Container(
                height: 48,
                decoration: BoxDecoration(
                  color: const Color(0xFFF3F4F6),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _isLoading ? null : _requestPinReset,
                    borderRadius: BorderRadius.circular(12),
                    child: Center(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.help_outline,
                            color: const Color(0xFF6B7280),
                            size: 18,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Forgot PIN?',
                            style: TextStyle(
                              color: const Color(0xFF6B7280),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Security Notice
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFFF0F9FF),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFFBAE6FD),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: const Color(0xFF0284C7),
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Your PIN is encrypted and secure. Contact admin if you need assistance.',
                  style: TextStyle(
                    color: const Color(0xFF0284C7),
                    fontWeight: FontWeight.w500,
                    fontSize: 13,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPinForm(ThemeData theme) {
    return _buildModernPinForm(theme);
  }

  void _requestPinReset() {
    setState(() {
      _showWhatsAppSupport = true;
      _errorMessage = 'Please contact admin via WhatsApp to reset your PIN.';
    });
  }

  Widget _buildModernRegistrationForm(ThemeData theme) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Registration Icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFFEC4899), Color(0xFFF59E0B)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFEC4899).withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: const Icon(
            Icons.person_add,
            color: Colors.white,
            size: 40,
          ),
        ),

        const SizedBox(height: 24),

        // Phone Number Field
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFF9FAFB),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: const Color(0xFFE5E7EB),
              width: 1,
            ),
          ),
          child: TextField(
            controller: _phoneController,
            keyboardType: TextInputType.phone,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'[0-9+]')),
            ],
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Color(0xFF1F2937),
            ),
            decoration: InputDecoration(
              hintText: 'Phone Number',
              hintStyle: TextStyle(
                color: const Color(0xFF9CA3AF),
                fontWeight: FontWeight.w400,
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.all(12),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFEC4899),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.phone,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 20,
              ),
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Full Name Field
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFF9FAFB),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: const Color(0xFFE5E7EB),
              width: 1,
            ),
          ),
          child: TextField(
            controller: _nameController,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Color(0xFF1F2937),
            ),
            decoration: InputDecoration(
              hintText: 'Enter your full name',
              hintStyle: TextStyle(
                color: const Color(0xFF9CA3AF),
                fontWeight: FontWeight.w400,
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.all(12),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFF59E0B),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.person,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 20,
              ),
            ),
          ),
        ),

        const SizedBox(height: 24),

        // Submit Registration Button
        Container(
          width: double.infinity,
          height: 56,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFFEC4899), Color(0xFFF59E0B)],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFEC4899).withOpacity(0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _isLoading ? null : _register,
              borderRadius: BorderRadius.circular(16),
              child: Center(
                child: _isLoading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Text(
                            'Submit Registration',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(width: 8),
                          const Icon(
                            Icons.person_add,
                            color: Colors.white,
                            size: 20,
                          ),
                        ],
                      ),
              ),
            ),
          ),
        ),

        const SizedBox(height: 20),

        // Info Notice
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFFFEF3C7),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFFFDE68A),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: const Color(0xFFD97706),
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'After registration, admin will provide you with a PIN to login',
                  style: TextStyle(
                    color: const Color(0xFFD97706),
                    fontWeight: FontWeight.w500,
                    fontSize: 13,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // Action Buttons
        Row(
          children: [
            Expanded(
              child: Container(
                height: 48,
                decoration: BoxDecoration(
                  color: const Color(0xFFF3F4F6),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _isLoading ? null : () => setState(() => _showRegistrationForm = false),
                    borderRadius: BorderRadius.circular(12),
                    child: Center(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.arrow_back,
                            color: const Color(0xFF6B7280),
                            size: 18,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Back to Login',
                            style: TextStyle(
                              color: const Color(0xFF6B7280),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Container(
                height: 48,
                decoration: BoxDecoration(
                  color: const Color(0xFFF3F4F6),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _launchWhatsAppSupport,
                    borderRadius: BorderRadius.circular(12),
                    child: Center(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.chat,
                            color: const Color(0xFF6B7280),
                            size: 18,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Support',
                            style: TextStyle(
                              color: const Color(0xFF6B7280),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRegistrationForm(ThemeData theme) {
    return _buildModernRegistrationForm(theme);
  }

  Widget _buildThankYou(ThemeData theme) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(Icons.check_circle, color: theme.colorScheme.primary, size: 64),
        const SizedBox(height: 16),
        Text(
          'Thank you for registering!',
          style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Text(
          'Your registration request has been sent to the admin for approval.',
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Once approved, you will receive a 4-digit PIN that you can use to log in.',
          textAlign: TextAlign.center,
          style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurface.withOpacity(0.7)),
        ),
        const SizedBox(height: 24),
        KFTButton(
          label: 'Contact Admin via WhatsApp',
          icon: Icons.chat,
          onPressed: _launchWhatsAppSupport,
          type: KFTButtonType.secondary,
          isFullWidth: true,
        ),
        const SizedBox(height: 16),
        KFTButton(
          label: 'Back to Login',
          icon: Icons.login,
          onPressed: () => setState(() {
            _showThankYou = false;
            _showRegistrationForm = false;
          }),
          type: KFTButtonType.primary,
          isFullWidth: true,
        ),
      ],
    );
  }

  void _launchWhatsAppSupport() async {
    final url = 'https://wa.me/$_whatsappNumber';
    if (await canLaunch(url)) {
      await launch(url);
    }
  }

  Future<void> _register() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });
    final name = _nameController.text.trim();
    final phone = _phoneController.text.trim();
    if (phone.isEmpty || phone.length < 10) {
      setState(() {
        _errorMessage = 'Please enter a valid phone number (at least 10 digits)';
        _isLoading = false;
      });
      return;
    }
    if (name.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter your full name';
        _isLoading = false;
      });
      return;
    }
    try {
      // Check if phone already exists before registering
      final exists = await checkPhoneRegistered(phone, await getDeviceId());
      if (exists) {
        setState(() {
          _errorMessage = 'This phone number is already registered. Please use the login form instead.';
          _isLoading = false;
        });
        return;
      }

      // Get device ID for registration
      final deviceId = await getDeviceId();
      await _apiService.register(name, phone, deviceId: deviceId);
      setState(() {
        _showThankYou = true;
        _showWhatsAppSupport = true;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Registration request sent successfully. Admin will review your request.'),
          duration: Duration(seconds: 5),
        ),
      );
    } catch (e) {
      final errorStr = e.toString().toLowerCase();

      // Check for network-related errors
      if (errorStr.contains('socketexception') ||
          errorStr.contains('connection refused') ||
          errorStr.contains('certificate_verify_failed') ||
          errorStr.contains('handshakeexception') ||
          errorStr.contains('network error') ||
          errorStr.contains('timeout')) {

        // Show network error dialog with retry option
        if (mounted) {
          context.showNetworkError(
            e.toString(),
            onRetry: () => _register(),
          );
        }
      } else {
        String errorMsg = e.toString().replaceAll('Exception: ', '');
        if (errorMsg.contains('Registration is currently disabled')) {
          errorMsg = 'Registration is currently disabled. Please contact admin for assistance.';
          _showWhatsAppSupport = true;
        }
        setState(() {
          _errorMessage = errorMsg;
        });
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}

class GlassCard extends StatelessWidget {
  final Widget child;
  const GlassCard({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.18),
        borderRadius: BorderRadius.circular(28),
        border: Border.all(color: Colors.white.withOpacity(0.25), width: 1.2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(28),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 12, sigmaY: 12),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 18),
            child: child,
          ),
        ),
      ),
    );
  }
}

class EnhancedGlassCard extends StatelessWidget {
  final Widget child;
  const EnhancedGlassCard({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(32),
        border: Border.all(color: Colors.white.withOpacity(0.3), width: 1.5),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 24,
            offset: const Offset(0, 12),
          ),
          BoxShadow(
            color: Colors.white.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(32),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 16, sigmaY: 16),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withOpacity(0.2),
                  Colors.white.withOpacity(0.1),
                ],
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 24),
              child: child,
            ),
          ),
        ),
      ),
    );
  }
}

class ColoredCard extends StatelessWidget {
  final Color color;
  final Color borderColor;
  final IconData icon;
  final Color iconColor;
  final String text;
  final Color textColor;
  const ColoredCard({
    Key? key,
    required this.color,
    required this.borderColor,
    required this.icon,
    required this.iconColor,
    required this.text,
    required this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(14),
        border: Border.all(color: borderColor, width: 1),
      ),
      child: Row(
        children: [
          Icon(icon, color: iconColor, size: 20),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: textColor,
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
