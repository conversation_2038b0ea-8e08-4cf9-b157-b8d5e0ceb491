import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../models/course_video.dart';
import '../models/user_profile.dart';
import '../models/course.dart';
import '../services/api_service.dart';
import '../utils/video_security_helper.dart';
import '../widgets/official_vimeo_player.dart';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:js' as js;

class ComprehensiveVideoPlayerPage extends StatefulWidget {
  final CourseVideo initialVideo;
  final Course? course;
  final UserProfile? userProfile;
  final bool autoPlay;

  const ComprehensiveVideoPlayerPage({
    Key? key,
    required this.initialVideo,
    this.course,
    this.userProfile,
    this.autoPlay = true,
  }) : super(key: key);

  @override
  State<ComprehensiveVideoPlayerPage> createState() => _ComprehensiveVideoPlayerPageState();
}

class _ComprehensiveVideoPlayerPageState extends State<ComprehensiveVideoPlayerPage>
    with TickerProviderStateMixin {

  // Current video state
  CourseVideo _currentVideo = CourseVideo(
    id: 0,
    title: '',
    description: '',
    videoUrl: '',
    weekNumber: 1,
    sequenceNumber: 1,
    isUnlocked: false,
    isCompleted: false,
  );

  // Course videos list
  List<CourseVideo> _courseVideos = [];
  Map<int, List<CourseVideo>> _videosByWeek = {};

  // Services
  final ApiService _apiService = ApiService();

  // Loading states
  bool _isLoadingVideos = true;
  bool _hasError = false;
  String _errorMessage = '';

  // Animation controllers
  late AnimationController _fadeAnimationController;
  late Animation<double> _fadeAnimation;

  // Orientation tracking
  bool _isInLandscape = false;

  // Video player key for preserving state during orientation changes
  final GlobalKey _videoPlayerKey = GlobalKey();
  Orientation? _lastOrientation;

  bool _miuiDialogShown = false;

  bool _isPlaying = false;
  // Reference to the video player for controlling playback
  final GlobalKey _videoPlayerStateKey = GlobalKey();
  // Fullscreen state for video player
  bool _isFullscreen = false;

  @override
  void initState() {
    super.initState();
    _currentVideo = widget.initialVideo;

    _initializeAnimations();
    _loadCourseVideos();
    _checkAndShowMiuiDialog();
  }

  void _initializeAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimationController.forward();
  }

  @override
  void dispose() {
    _fadeAnimationController.dispose();
    // Restore portrait orientation and overlays when leaving the page
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    super.dispose();
  }

  // Handle back button press - switch to portrait if in landscape, otherwise exit
  Future<bool> _handleBackButton() async {
    // Check current orientation
    final orientation = MediaQuery.of(context).orientation;

    if (orientation == Orientation.landscape || _isInLandscape) {
      debugPrint('🔄 Back button pressed in landscape - switching to portrait');

      // Restore status bar when exiting landscape via back button
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.manual,
        overlays: [
          SystemUiOverlay.top,
          SystemUiOverlay.bottom,
        ],
      );

      // Switch to portrait orientation
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);

      // Update landscape state
      setState(() {
        _isInLandscape = false;
      });

      // Exit fullscreen if in fullscreen mode
      await _exitFullscreenIfNeeded();

      // Don't exit the page, just switch orientation
      return false;
    } else {
      debugPrint('🔄 Back button pressed in portrait - exiting player');
      // In portrait mode, allow normal back navigation
      return true;
    }
  }

  // Exit fullscreen mode if currently in fullscreen
  Future<void> _exitFullscreenIfNeeded() async {
    try {
      // Try to exit fullscreen via JavaScript if possible
      debugPrint('🔄 Attempting to exit fullscreen mode');

      // Use JavaScript to exit fullscreen in the video player
      // This will trigger the onExitFullscreen callback which will update _isInLandscape
      await _executeJavaScriptToExitFullscreen();
    } catch (e) {
      debugPrint('❌ Error exiting fullscreen: $e');
    }
  }

  // Execute JavaScript to exit fullscreen mode
  Future<void> _executeJavaScriptToExitFullscreen() async {
    try {
      // We'll need to access the SimpleVimeoPlayer's WebView controller
      // For now, we'll rely on the orientation change to handle this
      debugPrint('🔄 JavaScript fullscreen exit - relying on orientation change');
    } catch (e) {
      debugPrint('❌ Error executing JavaScript to exit fullscreen: $e');
    }
  }

  Future<void> _loadCourseVideos() async {
    if (widget.course == null) {
      setState(() {
        _isLoadingVideos = false;
        _hasError = true;
        _errorMessage = 'Course information not available';
      });
      return;
    }

    try {
      setState(() {
        _isLoadingVideos = true;
        _hasError = false;
      });

      final response = await _apiService.makeApiRequest(
        'course_videos.php?course_id=${widget.course!.id}',
      );

      if (response['success'] == true && response['videos'] != null) {
        final List<dynamic> videosData = response['videos'];
        _courseVideos = videosData.map((data) => CourseVideo.fromJson(data)).toList();

        // Group videos by week
        _groupVideosByWeek();

        setState(() {
          _isLoadingVideos = false;
        });
      } else {
        throw Exception('Failed to load course videos');
      }
    } catch (e) {
      debugPrint('Error loading course videos: $e');
      setState(() {
        _isLoadingVideos = false;
        _hasError = true;
        _errorMessage = 'Failed to load course videos: $e';
      });
    }
  }

  void _groupVideosByWeek() {
    _videosByWeek.clear();
    for (final video in _courseVideos) {
      if (!_videosByWeek.containsKey(video.weekNumber)) {
        _videosByWeek[video.weekNumber] = [];
      }
      _videosByWeek[video.weekNumber]!.add(video);
    }

    // Sort videos within each week by sequence number
    _videosByWeek.forEach((week, videos) {
      videos.sort((a, b) => a.sequenceNumber.compareTo(b.sequenceNumber));
    });
  }

  void _switchToVideo(CourseVideo video) {
    if (!video.isUnlocked) {
      _showVideoLockedDialog(video);
      return;
    }

    setState(() {
      _currentVideo = video;
    });

    // Animate the transition
    _fadeAnimationController.reset();
    _fadeAnimationController.forward();
  }

  void _showVideoLockedDialog(CourseVideo video) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Video Locked'),
        content: Text(
          'This video will be unlocked on ${video.unlockDate ?? 'a future date'}.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Handle video completion and trigger immediate data refresh
  Future<void> _handleVideoCompletion() async {
    try {
      // Mark the current video as completed locally
      setState(() {
        _currentVideo = CourseVideo(
          id: _currentVideo.id,
          title: _currentVideo.title,
          description: _currentVideo.description,
          videoUrl: _currentVideo.videoUrl,
          thumbnailUrl: _currentVideo.thumbnailUrl,
          durationMinutes: _currentVideo.durationMinutes,
          weekNumber: _currentVideo.weekNumber,
          sequenceNumber: _currentVideo.sequenceNumber,
          isUnlocked: _currentVideo.isUnlocked,
          isCompleted: true, // Mark as completed
          unlockDate: _currentVideo.unlockDate,
          completionDate: DateTime.now().toIso8601String(),
          watchDurationSeconds: _currentVideo.watchDurationSeconds,
          lastPositionSeconds: _currentVideo.lastPositionSeconds,
          videoProvider: _currentVideo.videoProvider,
          videoEmbedUrl: _currentVideo.videoEmbedUrl,
          videoId: _currentVideo.videoId,
        );
      });

      // Update the video in the course videos list
      if (_courseVideos.isNotEmpty) {
        for (int i = 0; i < _courseVideos.length; i++) {
          if (_courseVideos[i].id == _currentVideo.id) {
            _courseVideos[i] = _currentVideo;
            break;
          }
        }
        _groupVideosByWeek();
      }

      debugPrint('Video completion handled locally: ${_currentVideo.title}');
    } catch (e) {
      debugPrint('Error handling video completion: $e');
    }
  }

  void _ensureImmersiveModeIfLandscape() {
    final orientation = MediaQuery.of(context).orientation;
    if (orientation == Orientation.landscape) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      Future.delayed(const Duration(milliseconds: 300), () {
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Main page content
        _buildMainContent(),
        // Fullscreen video overlay
        if (_isFullscreen)
          Positioned.fill(
            child: Container(
              color: Colors.black,
              child: Stack(
                children: [
                  // Video player that fills the entire screen
                  SizedBox(
                    width: double.infinity,
                    height: double.infinity,
                    child: OfficialVimeoPlayer(
                      key: _videoPlayerStateKey,
                      video: _currentVideo,
                      autoPlay: true,
                      onPlay: () => setState(() => _isPlaying = true),
                      onPause: () => setState(() => _isPlaying = false),
                    ),
                  ),
                  // Restore/exit fullscreen button
                  Positioned(
                    top: 32,
                    right: 32,
                    child: GestureDetector(
                      onTap: () async {
                        setState(() => _isFullscreen = false);
                        // Restore portrait orientation
                        await SystemChrome.setPreferredOrientations([
                          DeviceOrientation.portraitUp,
                          DeviceOrientation.portraitDown,
                        ]);
                        // Restore system UI
                        SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(24),
                        ),
                        child: const Icon(Icons.fullscreen_exit, color: Colors.white, size: 32),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildMainContent() {
    final orientation = MediaQuery.of(context).orientation;
    final isLandscape = orientation == Orientation.landscape;
    if (isLandscape) {
      _ensureImmersiveModeIfLandscape();
    }

    // Detect orientation changes to preserve video state
    if (_lastOrientation != null && _lastOrientation != orientation) {
      debugPrint('🔄 Orientation changed from $_lastOrientation to $orientation - preserving video state');
      // The SimpleVimeoPlayer will handle position preservation internally
    }
    _lastOrientation = orientation;

    return PopScope(
      canPop: false, // We'll handle the back button manually
      onPopInvoked: (bool didPop) async {
        if (!didPop) {
          final shouldPop = await _handleBackButton();
          if (shouldPop && context.mounted) {
            Navigator.of(context).pop(_currentVideo);
          }
        }
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: SafeArea(
          child: isLandscape
            ? _buildLandscapeLayout()
            : _buildPortraitLayout(),
        ),
      ),
    );
  }

  // Portrait layout with video player and list
  Widget _buildPortraitLayout() {
    return Column(
      children: [
        // Video player section with improved styling
        Container(
          width: double.infinity,
          height: MediaQuery.of(context).size.width * 9 / 16,
          decoration: BoxDecoration(
            color: Colors.black,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: _buildVideoPlayerSection(),
        ),
        
        // Video info and list section with modern design
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              children: [
                // Video info section
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 4,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Fullscreen button before title
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              _currentVideo.title,
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                height: 1.3,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          // Removed play/pause and fullscreen icons beside the title
                        ],
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // Video metadata row
                      Row(
                        children: [
                          // Week indicator
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.calendar_today,
                                  size: 14,
                                  color: Theme.of(context).primaryColor,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Week ${_currentVideo.weekNumber}',
                                  style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          
                          const SizedBox(width: 12),
                          
                          // Duration indicator
                          if (_currentVideo.durationMinutes != null)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.grey.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.access_time,
                                    size: 14,
                                    color: Colors.grey[600],
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${_currentVideo.durationMinutes} min',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontWeight: FontWeight.w500,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          
                          const Spacer(),
                          
                          // Completion status
                          if (_currentVideo.isCompleted)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.green.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.check_circle,
                                    size: 14,
                                    color: Colors.green[700],
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'Completed',
                                    style: TextStyle(
                                      color: Colors.green[700],
                                      fontWeight: FontWeight.w600,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // Video list section with improved styling
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: _buildVideoListSection(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Landscape layout - fullscreen video only
  Widget _buildLandscapeLayout() {
    return _buildVideoPlayerSection();
  }

  Widget _buildVideoPlayerSection() {
    final orientation = MediaQuery.of(context).orientation;
    final isLandscape = orientation == Orientation.landscape;

    return Stack(
      children: [
        // Video player
        FadeTransition(
          opacity: _fadeAnimation,
          child: OfficialVimeoPlayer(
            key: _videoPlayerStateKey,
            video: _currentVideo,
            autoPlay: widget.autoPlay,
            onCompleted: () {
              debugPrint('Video completed: ${_currentVideo.title}');
              // Mark video as completed and refresh course data
              _handleVideoCompletion();
              // Auto-advance to next video if available
              _autoAdvanceToNextVideo();
            },
            onError: (error) {
              debugPrint('Video player error: $error');
              _showErrorSnackBar('Video playback error: $error');
            },
            // Add callbacks to track fullscreen/orientation changes
            onReady: () {
              debugPrint('Video player ready: ${_currentVideo.title}');
              // Position restoration is handled by the player itself
            },
            onPlay: () {
              debugPrint('Video started playing');
              setState(() {
                _isPlaying = true;
              });
            },
            onPause: () {
              debugPrint('Video paused');
              setState(() {
                _isPlaying = false;
              });
            },
            onEnterFullscreen: () {
              debugPrint('🔄 Video entered fullscreen - tracking landscape state');
              setState(() {
                _isInLandscape = true;
              });
              _ensureImmersiveModeIfLandscape(); // Extra reliability
            },
            onExitFullscreen: () {
              debugPrint('🔄 Video exited fullscreen - tracking portrait state');
              setState(() {
                _isInLandscape = false;
              });
            },
          ),
        ),

        // Back button overlay - always show
        Positioned(
          top: 16,
          left: 16,
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () => Navigator.of(context).pop(_currentVideo),
              child: Container(
                padding: const EdgeInsets.all(8),
                child: Icon(
                  Icons.arrow_back,
                  color: Colors.white.withOpacity(0.7),
                  size: 24,
                ),
              ),
            ),
          ),
        ),

        // Right side controls - back button and restore fullscreen button
        if (isLandscape || _isInLandscape)
          Positioned(
            top: 16,
            right: 16,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Back button
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(20),
                    onTap: () => Navigator.of(context).pop(_currentVideo),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Icon(
                        Icons.arrow_back,
                        color: Colors.white.withOpacity(0.9),
                        size: 24,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Restore fullscreen button
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(20),
                    onTap: () async {
                      if (kIsWeb) {
                        // Web: Exit fullscreen using JS
                        await _enterOrExitWebFullscreen();
                      } else {
                        // Mobile: Switch back to portrait
                        await SystemChrome.setPreferredOrientations([
                          DeviceOrientation.portraitUp,
                          DeviceOrientation.portraitDown,
                        ]);
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Icon(
                        Icons.fullscreen_exit,
                        color: Colors.white.withOpacity(0.9),
                        size: 24,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildVideoListSection() {
    if (_isLoadingVideos) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: TextStyle(
                color: Colors.red[300],
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.only(top: 16, bottom: 24),
      itemCount: _videosByWeek.length,
      itemBuilder: (context, weekIndex) {
        final weekNumber = _videosByWeek.keys.elementAt(weekIndex);
        final weekVideos = _videosByWeek[weekNumber]!;
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Week header
            Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Text(
                'Week $weekNumber',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
            
            // Videos in this week
            ...weekVideos.map((video) => _buildVideoCard(video)),
            
            const SizedBox(height: 24),
          ],
        );
      },
    );
  }

  Widget _buildVideoCard(CourseVideo video) {
    final bool isCurrentVideo = video.id == _currentVideo.id;
    final bool isLocked = !video.isUnlocked;
    final bool isCompleted = video.isCompleted;
    final thumbnailUrl = (video.thumbnailUrl != null && video.thumbnailUrl!.isNotEmpty)
        ? video.thumbnailUrl!
        : video.getVimeoThumbnailUrl();

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: isCurrentVideo 
          ? Theme.of(context).primaryColor.withOpacity(0.1)
          : Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isCurrentVideo
            ? Theme.of(context).primaryColor
            : Colors.transparent,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _switchToVideo(video),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Video thumbnail with improved styling
              Container(
                width: 100,
                height: 56,
                decoration: BoxDecoration(
                  color: Colors.grey[800],
                  borderRadius: BorderRadius.circular(12),
                  image: thumbnailUrl.isNotEmpty
                    ? DecorationImage(
                        image: NetworkImage(thumbnailUrl),
                        fit: BoxFit.cover,
                      )
                    : null,
                ),
                child: Stack(
                  children: [
                    if (thumbnailUrl.isEmpty)
                      const Center(
                        child: Icon(
                          Icons.play_circle_outline,
                          color: Colors.white54,
                          size: 24,
                        ),
                      ),

                    // Status overlay with improved styling
                    Positioned(
                      bottom: 4,
                      right: 4,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          isLocked
                            ? Icons.lock
                            : isCompleted
                              ? Icons.check_circle
                              : isCurrentVideo
                                ? Icons.pause
                                : Icons.play_arrow,
                          color: isLocked
                            ? Colors.orange
                            : isCompleted
                              ? Colors.green
                              : Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 16),

              // Video details with improved typography
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      video.title,
                      style: TextStyle(
                        color: isLocked 
                          ? Colors.grey[400] 
                          : Theme.of(context).textTheme.titleMedium?.color,
                        fontSize: 15,
                        fontWeight: isCurrentVideo ? FontWeight.bold : FontWeight.w500,
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 8),

                    Row(
                      children: [
                        if (video.durationMinutes != null) ...[
                          Icon(
                            Icons.access_time,
                            size: 14,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${video.durationMinutes} min',
                            style: TextStyle(
                              color: Colors.grey[400],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 12),
                        ],

                        if (isLocked && video.unlockDate != null) ...[
                          Icon(
                            Icons.schedule,
                            size: 14,
                            color: Colors.orange[300],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Unlocks ${_formatUnlockDate(video.unlockDate!)}',
                            style: TextStyle(
                              color: Colors.orange[300],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ] else if (isCompleted) ...[
                          Icon(
                            Icons.check_circle,
                            size: 14,
                            color: Colors.green[300],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Completed',
                            style: TextStyle(
                              color: Colors.green[300],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatUnlockDate(String unlockDate) {
    try {
      final date = DateTime.parse(unlockDate);
      final now = DateTime.now();
      final difference = date.difference(now).inDays;

      if (difference <= 0) {
        return 'now';
      } else if (difference == 1) {
        return 'tomorrow';
      } else {
        return 'in $difference days';
      }
    } catch (e) {
      return unlockDate;
    }
  }

  void _autoAdvanceToNextVideo() {
    // Find the next unlocked video in sequence
    final currentIndex = _courseVideos.indexWhere((v) => v.id == _currentVideo.id);
    if (currentIndex >= 0 && currentIndex < _courseVideos.length - 1) {
      for (int i = currentIndex + 1; i < _courseVideos.length; i++) {
        if (_courseVideos[i].isUnlocked) {
          // Show auto-advance dialog
          _showAutoAdvanceDialog(_courseVideos[i]);
          break;
        }
      }
    }
  }

  void _showAutoAdvanceDialog(CourseVideo nextVideo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Video Completed!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Great job! Would you like to continue with the next video?'),
            const SizedBox(height: 12),
            Text(
              'Next: ${nextVideo.title}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Stay Here'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _switchToVideo(nextVideo);
            },
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Future<void> _checkAndShowMiuiDialog() async {
    if (_miuiDialogShown) return;
    if (!Platform.isAndroid) return;
    final deviceInfo = DeviceInfoPlugin();
    final androidInfo = await deviceInfo.androidInfo;
    final brand = androidInfo.brand?.toLowerCase() ?? '';
    final manufacturer = androidInfo.manufacturer?.toLowerCase() ?? '';
    final miui = brand.contains('xiaomi') || brand.contains('redmi') || manufacturer.contains('xiaomi') || manufacturer.contains('redmi');
    if (miui && mounted) {
      _miuiDialogShown = true;
      // ignore: use_build_context_synchronously
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('MIUI/Redmi Fullscreen Tip'),
          content: const Text(
            'On Xiaomi/Redmi phones, to hide the navigation bar in fullscreen, go to Settings > Display > Fullscreen mode and enable it for this app.'
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }

  Future<void> _enterOrExitWebFullscreen() async {
    try {
      // Use JS interop to toggle fullscreen on the main document element
      js.context.callMethod('eval', [
        '''
        if (!document.fullscreenElement) {
          document.documentElement.requestFullscreen();
        } else {
          document.exitFullscreen();
        }
        '''
      ]);
    } catch (e) {
      debugPrint('Web fullscreen API error: $e');
    }
  }

  void _togglePlayPause() {
    // Toggle the playing state
    setState(() {
      _isPlaying = !_isPlaying;
    });
    
    // Control the actual video playback using JavaScript
    if (kIsWeb) {
      // For web, use JavaScript to control the Vimeo player
      js.context.callMethod('eval', [
        '''
        const iframe = document.querySelector('iframe');
        if (iframe && iframe.contentWindow) {
          iframe.contentWindow.postMessage('{"method":"${_isPlaying ? 'play' : 'pause'}"}', '*');
        }
        '''
      ]);
    }
  }
}
