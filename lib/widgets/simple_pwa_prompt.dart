import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/simple_pwa_service.dart';
import '../design_system/kft_design_system.dart';
import 'simple_pwa_dialog.dart';
import 'mobile_pwa_dialog.dart';
import 'pwa_install_banner.dart';
import 'dart:html' as html;

/// Enhanced PWA prompt widget with persistent installation prompts
/// Shows install dialog on page load and provides floating install button
/// Matches the Flutter app's design system and provides professional UX
class SimplePWAPrompt extends StatefulWidget {
  final Widget child;

  const SimplePWAPrompt({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<SimplePWAPrompt> createState() => _SimplePWAPromptState();
}

class _SimplePWAPromptState extends State<SimplePWAPrompt> {
  final SimplePWAService _pwaService = SimplePWAService();
  bool _hasShownDialog = false;

  @override
  void initState() {
    super.initState();
    if (kIsWeb) {
      _pwaService.initialize();
      _setupDialogListener();
    }
  }

  /// Set up listener for showing the dialog
  void _setupDialogListener() {
    _pwaService.shouldShowDialog.addListener(_handleDialogShow);
  }

  /// Handle showing the dialog when needed
  void _handleDialogShow() {
    if (_pwaService.shouldShowDialog.value && !_hasShownDialog && mounted) {
      _hasShownDialog = true;
      _pwaService.markDialogShown();

      // Show dialog after a brief delay to ensure UI is ready
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _showInstallDialog();
        }
      });
    }
  }

  /// Show the install dialog
  Future<void> _showInstallDialog() async {
    // Show mobile-specific dialog for mobile devices, regular dialog for desktop
    if (_pwaService.isMobile) {
      await MobilePWADialog.show(
        context,
        onDismiss: () {
          debugPrint('📋 Mobile PWA dialog dismissed');
          _resetDialogFlag();
        },
      );
    } else {
      await SimplePWADialog.show(
        context,
        onInstallSuccess: () {
          debugPrint('🎉 PWA installation successful from dialog');
        },
        onDismiss: () {
          debugPrint('📋 PWA dialog dismissed');
          _resetDialogFlag();
        },
      );
    }
  }

  /// Reset dialog flag to allow showing again later
  void _resetDialogFlag() {
    Future.delayed(const Duration(minutes: 5), () {
      if (mounted) {
        _hasShownDialog = false;
      }
    });
  }

  @override
  void dispose() {
    _pwaService.shouldShowDialog.removeListener(_handleDialogShow);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Main app content
        Column(
          children: [
            // PWA install banner at the top
            if (kIsWeb) const PWAInstallBanner(),
            // Main app content
            Expanded(child: widget.child),
          ],
        ),
        if (kIsWeb) ...[
          // Floating install button
          ValueListenableBuilder<bool>(
            valueListenable: _pwaService.installPromptAvailable,
            builder: (context, available, _) {
              debugPrint('🔔 PWA installPromptAvailable: $available');
              if (!available) return const SizedBox.shrink();
              return _buildFloatingInstallButton(context);
            },
          ),
          // Development/testing buttons (only in debug mode)
          if (kDebugMode) _buildDebugButtons(context),
        ],
      ],
    );
  }

  /// Build the floating install button
  Widget _buildFloatingInstallButton(BuildContext context) {
    return Positioned(
      bottom: KFTDesignSystem.spacingXl,
      right: KFTDesignSystem.spacingLg,
      child: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusMd),
        shadowColor: KFTDesignSystem.primaryColor.withOpacity(0.3),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                KFTDesignSystem.primaryColor,
                KFTDesignSystem.primaryColor.withOpacity(0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusMd),
          ),
          child: ElevatedButton.icon(
            icon: const Icon(Icons.download_rounded, size: 20),
            label: const Text('Install App'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              foregroundColor: Colors.white,
              shadowColor: Colors.transparent,
              padding: const EdgeInsets.symmetric(
                horizontal: KFTDesignSystem.spacingLg,
                vertical: KFTDesignSystem.spacingMd,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusMd),
              ),
              textStyle: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
            onPressed: () async {
              debugPrint('🟢 Floating install button pressed (Mobile: ${_pwaService.isMobile})');

              // For mobile devices, directly show instructions
              if (_pwaService.isMobile) {
                _showInstallDialog();
              } else {
                // For desktop, try native prompt first
                final success = await _pwaService.promptInstall();
                if (!success && mounted) {
                  _showInstallDialog();
                }
              }
            },
          ),
        ),
      ),
    );
  }

  /// Build debug buttons for testing (only shown in debug mode)
  Widget _buildDebugButtons(BuildContext context) {
    return Positioned(
      bottom: KFTDesignSystem.spacingXl,
      left: KFTDesignSystem.spacingLg,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            onPressed: () async {
              debugPrint('🟠 Force dialog button pressed');
              _pwaService.forceShowDialog();
            },
            child: const Text('Show Dialog', style: TextStyle(fontSize: 12)),
          ),
          const SizedBox(height: 8),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            onPressed: () async {
              debugPrint('🔄 Reset PWA state');
              await _pwaService.resetState();
            },
            child: const Text('Reset State', style: TextStyle(fontSize: 12)),
          ),
        ],
      ),
    );
  }
}
