<?php
require_once 'includes/config.php';
require_once 'includes/database.php';

// Add CORS headers for local development
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['phone_number'])) {
    $inputPhone = preg_replace('/[^0-9]/', '', $_POST['phone_number']);
    $deviceId = isset($_POST['device_id']) ? $_POST['device_id'] : null;
    if (strlen($inputPhone) >= 10) {
        $db = new Database();
        $conn = $db->getConnection();
        // Try to match with and without country code
        $possiblePhones = [];
        if (strlen($inputPhone) === 10) {
            $possiblePhones[] = $inputPhone;
            $possiblePhones[] = '+91' . $inputPhone;
        } elseif (strlen($inputPhone) > 10) {
            $possiblePhones[] = $inputPhone;
            // If starts with 91, also try without
            if (substr($inputPhone, 0, 2) === '91') {
                $possiblePhones[] = substr($inputPhone, 2);
                $possiblePhones[] = '+91' . substr($inputPhone, 2);
            }
        }
        $query = "SELECT id, name, username, device_id FROM users WHERE phone = ? OR phone_number = ? LIMIT 1";
        foreach ($possiblePhones as $phoneVal) {
            $stmt = $conn->prepare($query);
            $stmt->bind_param('ss', $phoneVal, $phoneVal);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($row = $result->fetch_assoc()) {
                // Device restriction logic
                if (!empty($row['device_id']) && $deviceId && $row['device_id'] !== $deviceId) {
                    echo json_encode(['error' => 'DEVICE_ALREADY_REGISTERED']);
                    exit;
                }
                echo json_encode([
                    'exists' => true,
                    'name' => $row['name'],
                    'username' => $row['username']
                ]);
                exit;
            }
            $stmt->close();
        }
    }
}
echo json_encode(['exists' => false]); 