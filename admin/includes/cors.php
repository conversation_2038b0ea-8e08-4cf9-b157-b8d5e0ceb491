<?php
/**
 * CORS Middleware
 *
 * This file handles Cross-Origin Resource Sharing (CORS) headers
 * to allow API requests from different origins.
 */

// Suppress warnings in API responses
error_reporting(E_ERROR | E_PARSE);

// Define allowed origins
// In production, you would list specific domains instead of allowing all origins
$allowedOrigins = [
    'http://localhost:9002',  // Flutter web app (current setup)
    'http://localhost:9001',  // Backend server (current setup)
    'http://localhost:8001',  // Flutter web app (legacy)
    'http://localhost:8000',  // Admin dashboard (legacy)
    'http://localhost:3000',  // For potential future development
    'http://127.0.0.1:9002',  // Flutter web app (127.0.0.1)
    'http://127.0.0.1:9001',  // Backend server (127.0.0.1)
    'capacitor://localhost',  // For Capacitor mobile apps
    'ionic://localhost',      // For Ionic mobile apps
    'http://***********:8001', // Local IP Flutter web app
    'http://***********:8000', // Local IP Admin dashboard
    'http://***************:9002', // New IP Flutter web app (current)
    'http://***************:9001', // New IP Backend server (current)
    'http://***************:8001', // New IP Flutter web app (legacy)
    'http://***************:8000', // New IP Admin dashboard (legacy)
    'http://***************:8080', // New IP Backend server (legacy)
    'file://',                // For mobile apps
    'null',                   // For some mobile app requests
];

/**
 * Handle CORS (Cross-Origin Resource Sharing) for API requests
 */
function handleCors() {
    global $allowedOrigins;

    // Get the origin header
    $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';

    // Check if the origin is allowed
    if (in_array($origin, $allowedOrigins) || true) { // The "|| true" allows all origins for development
        header("Access-Control-Allow-Origin: $origin");
        header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
        header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
        header("Access-Control-Allow-Credentials: true");
        header("Access-Control-Max-Age: 86400"); // 24 hours cache
    }

    // Handle preflight OPTIONS request
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        // Return 200 OK status for preflight requests
        http_response_code(200);
        exit;
    }
}

// Apply CORS headers for direct inclusion
$origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';

// For mobile apps, the origin might be null or file://
if (empty($origin) || $origin === 'null' || strpos($origin, 'file://') === 0) {
    // Allow any origin for mobile apps
    header("Access-Control-Allow-Origin: *");
} else if (in_array($origin, $allowedOrigins) || true) { // The "|| true" allows all origins for development
    header("Access-Control-Allow-Origin: $origin");
    header("Access-Control-Allow-Credentials: true");
}

// Common headers for all requests
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header("Access-Control-Expose-Headers: Authorization"); // Expose Authorization header
header("Access-Control-Max-Age: 86400"); // 24 hours cache

// Prevent caching for API responses
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    // Return 200 OK status for preflight requests
    http_response_code(200);
    exit;
}
