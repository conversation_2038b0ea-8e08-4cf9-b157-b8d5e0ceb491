<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$response = [
    'success' => true,
    'message' => 'KFT Fitness API is running',
    'timestamp' => date('Y-m-d H:i:s'),
    'server_time' => time(),
    'available_endpoints' => [
        'login.php' => 'POST - User login with phone and PIN',
        'ping.php' => 'GET - Server connectivity test',
        'profile.php' => 'GET - User profile information',
        'courses.php' => 'GET - Available courses',
        'course_videos.php' => 'GET - Course videos',
        'video_progress.php' => 'POST - Update video progress',
        'quotes.php' => 'GET - Motivational quotes',
        'water_reminder.php' => 'POST - Water reminder settings',
        'calorie_logs.php' => 'POST - Calorie tracking',
        'streak.php' => 'POST - Workout streak tracking'
    ],
    'base_url' => 'http://localhost:9001/admin/api/',
    'status' => 'operational'
];

echo json_encode($response, JSON_PRETTY_PRINT);
?> 