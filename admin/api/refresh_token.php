<?php
/**
 * Token Refresh API Endpoint
 * Handles JWT token refresh for persistent authentication
 */

// Set headers for JSON response and CORS
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Include required files
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/jwt.php';
require_once '../includes/utilities.php';

// Function to return JSON response is already defined in config.php

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        returnResponse(['success' => false, 'error' => 'Invalid JSON input'], 400);
    }

    // Validate required fields
    if (!isset($input['refresh_token']) || empty($input['refresh_token'])) {
        returnResponse(['success' => false, 'error' => 'Refresh token is required'], 400);
    }

    $refreshToken = $input['refresh_token'];

    // Verify refresh token
    try {
        $refreshPayload = verify_jwt($refreshToken, APP_SECRET . '_refresh');
        
        if (!$refreshPayload || !isset($refreshPayload['user_id']) || $refreshPayload['type'] !== 'refresh') {
            returnResponse(['success' => false, 'error' => 'Invalid refresh token'], 401);
        }

        // Check if refresh token is expired
        if (isset($refreshPayload['exp']) && $refreshPayload['exp'] < time()) {
            returnResponse(['success' => false, 'error' => 'Refresh token expired'], 401);
        }

        $userId = $refreshPayload['user_id'];

    } catch (Exception $e) {
        error_log('Refresh token verification failed: ' . $e->getMessage());
        returnResponse(['success' => false, 'error' => 'Invalid refresh token'], 401);
    }

    // Initialize database
    $db = new Database();
    
    // Get user data
    $user = $db->getUserById($userId);
    if (!$user) {
        returnResponse(['success' => false, 'error' => 'User not found'], 404);
    }

    // Generate new access token with extended expiration
    $tokenExpiry = time() + (300 * 24 * 60 * 60); // 300 days
    
    $jwtPayload = [
        'user_id' => $user['id'],
        'name' => $user['name'],
        'iat' => time(),
        'exp' => $tokenExpiry,
        'extended' => true,
        'refreshed' => true
    ];

    $newToken = generate_jwt($jwtPayload, APP_SECRET);

    // Generate new refresh token
    $newRefreshTokenPayload = [
        'user_id' => $user['id'],
        'type' => 'refresh',
        'iat' => time(),
        'exp' => time() + (365 * 24 * 60 * 60) // 1 year for refresh token
    ];
    $newRefreshToken = generate_jwt($newRefreshTokenPayload, APP_SECRET . '_refresh');

    // Update user's last login timestamp
    $db->updateUserLastLogin($user['id']);

    // Enhanced logging in development mode
    if (defined('DEV_MODE') && DEV_MODE === true) {
        error_log('=== TOKEN REFRESH ===');
        error_log('User ID: ' . $user['id']);
        error_log('User Name: ' . $user['name']);
        error_log('Current time: ' . time() . ' (' . date('Y-m-d H:i:s') . ')');
        error_log('New token expiry: ' . $tokenExpiry . ' (' . date('Y-m-d H:i:s', $tokenExpiry) . ')');
        error_log('New token (first 50 chars): ' . substr($newToken, 0, 50) . '...');
        error_log('=== END TOKEN REFRESH ===');
    }

    // Return new tokens
    returnResponse([
        'success' => true,
        'token' => $newToken,
        'refresh_token' => $newRefreshToken,
        'expires_at' => date('Y-m-d H:i:s', $tokenExpiry),
        'user' => [
            'id' => $user['id'],
            'name' => $user['name'],
            'username' => $user['username'],
            'email' => $user['email'],
            'phone_number' => $user['phone_number'],
        ]
    ]);

} catch (Exception $e) {
    error_log('Token refresh error: ' . $e->getMessage());
    returnResponse(['success' => false, 'error' => 'Internal server error'], 500);
}
?>
